# Currency Conversion Issue Report

## 🚨 Problem Summary

The Stripe payment integration is showing incorrect currency conversion from TND (Tunisian Dinar) to EUR (Euro), resulting in customers being charged significantly more than expected.

## 📊 Observed Issue

**Expected Behavior:**
- Customer order: 250.00 TND
- Expected charge: ~€75-80 (based on current exchange rate ~3.3 TND = 1 EUR)

**Actual Behavior:**
- Customer order: 250.00 TND
- Displayed amount: 25000.000 TND (100x multiplier error)
- Exchange rate shown: 1 EUR = 0.296 TND (inverted rate)
- Final charge: €7400.00 (way too much)

## 🔍 Root Cause Analysis

### Frontend Implementation
The frontend is correctly sending:
```javascript
// From payment.service.js
const paymentData = {
  amount: Math.round(250.00 * 100), // = 25000 (correct for Stripe cents)
  currency: 'eur',
  original_currency: 'TND',
  original_amount: 250.00,
  commande_id: orderId
};
```

### Expected Backend Processing
The backend should:
1. Receive `amount: 25000` (250.00 EUR in cents)
2. See `original_currency: 'TND'` and `original_amount: 250.00`
3. Convert 250.00 TND to EUR using correct exchange rate
4. Create Stripe payment intent with converted EUR amount

## 🐛 Suspected Issues

### 1. Double Amount Multiplication
**Problem:** Backend might be multiplying the amount by 100 again
```php
// ❌ WRONG - Frontend already multiplied by 100
$stripeAmount = $request->amount * 100; // This would make 25000 → 2500000

// ✅ CORRECT - Use amount as-is from frontend
$stripeAmount = $request->amount; // 25000 (already in cents)
```

### 2. Inverted Exchange Rate
**Problem:** Exchange rate calculation is inverted
```php
// ❌ WRONG - This gives 1 EUR = 0.296 TND
$exchangeRate = 1 / 3.3; // = 0.303

// ✅ CORRECT - Should be 1 EUR = 3.3 TND
$exchangeRate = 3.3; // Current market rate
```

### 3. Incorrect Currency Conversion Logic
**Problem:** Converting the wrong direction or using wrong base amount
```php
// ❌ WRONG - Converting already converted amount
$eurAmount = ($request->amount / 100) / $exchangeRate;

// ✅ CORRECT - Convert original TND amount to EUR
$tndAmount = $request->original_amount; // 250.00 TND
$eurAmount = $tndAmount / $exchangeRate; // 250.00 / 3.3 = ~75.76 EUR
$stripeAmount = round($eurAmount * 100); // Convert to cents: 7576
```

## 🔧 Recommended Backend Fixes

### 1. Update Payment Intent Creation
```php
// In your Stripe payment intent creation endpoint
public function createPaymentIntent(Request $request)
{
    $originalAmount = $request->original_amount; // 250.00 TND
    $originalCurrency = $request->original_currency; // 'TND'
    $targetCurrency = $request->currency; // 'eur'
    
    // Get current exchange rate (implement your preferred method)
    $exchangeRate = $this->getExchangeRate('TND', 'EUR'); // Should return ~3.3
    
    // Convert TND to EUR
    $eurAmount = $originalAmount / $exchangeRate; // 250.00 / 3.3 = ~75.76
    
    // Convert to Stripe format (cents)
    $stripeAmount = round($eurAmount * 100); // 7576 cents = €75.76
    
    // Create payment intent with correct amount
    $paymentIntent = \Stripe\PaymentIntent::create([
        'amount' => $stripeAmount,
        'currency' => 'eur',
        'metadata' => [
            'original_amount' => $originalAmount,
            'original_currency' => $originalCurrency,
            'exchange_rate' => $exchangeRate,
            'commande_id' => $request->commande_id
        ]
    ]);
    
    return response()->json([
        'success' => true,
        'client_secret' => $paymentIntent->client_secret,
        'payment_intent_id' => $paymentIntent->id,
        'amount' => $stripeAmount,
        'currency' => 'eur',
        'conversion_data' => [
            'original_amount' => $originalAmount,
            'original_currency' => $originalCurrency,
            'converted_amount' => $eurAmount,
            'converted_currency' => 'eur',
            'exchange_rate' => $exchangeRate
        ]
    ]);
}
```

### 2. Exchange Rate Service
```php
private function getExchangeRate($fromCurrency, $toCurrency)
{
    // Option 1: Use a reliable exchange rate API
    // $rate = $this->exchangeRateService->getRate($fromCurrency, $toCurrency);
    
    // Option 2: Use cached/database rates updated daily
    // $rate = ExchangeRate::where('from', $fromCurrency)
    //                    ->where('to', $toCurrency)
    //                    ->latest()
    //                    ->value('rate');
    
    // Option 3: Hardcoded rate (temporary solution)
    if ($fromCurrency === 'TND' && $toCurrency === 'EUR') {
        return 3.3; // Update this regularly
    }
    
    throw new Exception("Exchange rate not available for {$fromCurrency} to {$toCurrency}");
}
```

## 🧪 Testing Recommendations

### Test Cases to Verify
1. **250.00 TND order** should charge ~€75.76 (not €7400)
2. **Exchange rate display** should show ~3.3 TND = 1 EUR (not 0.296)
3. **Amount formatting** should show correct original and converted amounts
4. **Stripe payment** should process the correct EUR amount

### Debug Logging
Add logging to track the conversion process:
```php
Log::info('Payment conversion debug', [
    'original_amount' => $originalAmount,
    'original_currency' => $originalCurrency,
    'exchange_rate' => $exchangeRate,
    'converted_eur_amount' => $eurAmount,
    'stripe_amount_cents' => $stripeAmount,
    'frontend_sent_amount' => $request->amount
]);
```

## 📋 Frontend Changes Made

To help with debugging, the frontend now sends:
- `original_currency`: The source currency (TND)
- `original_amount`: The original amount in TND
- `currency`: Target currency for Stripe (EUR)
- `amount`: Amount in smallest unit (cents) for the target currency

## 🔄 Next Steps

1. **Implement the corrected conversion logic** in the backend
2. **Test with small amounts** first (e.g., 10 TND orders)
3. **Verify exchange rates** are current and correct
4. **Add comprehensive logging** for debugging
5. **Test end-to-end payment flow** before production deployment

## 📞 Contact

If you need clarification on any part of this issue or the frontend implementation, please reach out to the frontend team.

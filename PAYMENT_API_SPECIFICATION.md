# Payment API Specification for Currency Conversion

## 📡 API Endpoint: `/api/stripe/create-payment-intent`

### Request Format

The frontend sends the following payload:

```json
{
  "amount": 25000,
  "currency": "eur",
  "commande_id": 123,
  "customer_email": "<EMAIL>",
  "original_currency": "TND",
  "original_amount": 250.00,
  "shipping_address": {
    "street": "123 Main St",
    "city": "Tunis",
    "postal_code": "1000",
    "country": "Tunisia"
  },
  "metadata": {
    "customer_name": "John Doe",
    "items_count": 3,
    "order_id": 123
  }
}
```

### Field Explanations

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `amount` | Integer | **Already converted to target currency cents** | `25000` (€250.00 in cents) |
| `currency` | String | Target currency for Stripe (always EUR) | `"eur"` |
| `original_currency` | String | Source currency from order | `"TND"` |
| `original_amount` | Float | Original amount in source currency | `250.00` |
| `commande_id` | Integer | Order ID for tracking | `123` |

### ⚠️ Critical Notes

1. **`amount` field is NOT in TND** - it's already converted to EUR cents by frontend
2. **Use `original_amount` and `original_currency`** for the actual conversion
3. **Don't multiply `amount` by 100** - it's already in smallest currency unit

## 🔄 Correct Backend Processing Flow

### Step 1: Extract Original Values
```php
$originalAmount = $request->original_amount; // 250.00 TND
$originalCurrency = $request->original_currency; // 'TND'
$targetCurrency = $request->currency; // 'eur'
```

### Step 2: Get Exchange Rate
```php
$exchangeRate = $this->getExchangeRate($originalCurrency, $targetCurrency);
// Should return something like 3.3 (meaning 1 EUR = 3.3 TND)
```

### Step 3: Convert Currency
```php
$convertedAmount = $originalAmount / $exchangeRate; // 250.00 / 3.3 = 75.76 EUR
$stripeAmount = round($convertedAmount * 100); // 7576 cents
```

### Step 4: Create Stripe Payment Intent
```php
$paymentIntent = \Stripe\PaymentIntent::create([
    'amount' => $stripeAmount, // 7576 (€75.76 in cents)
    'currency' => $targetCurrency, // 'eur'
    'metadata' => [
        'original_amount' => $originalAmount,
        'original_currency' => $originalCurrency,
        'exchange_rate' => $exchangeRate,
        'commande_id' => $request->commande_id
    ]
]);
```

## 📤 Expected Response Format

```json
{
  "success": true,
  "client_secret": "pi_xxx_secret_xxx",
  "payment_intent_id": "pi_xxx",
  "amount": 7576,
  "currency": "eur",
  "paiement_id": 456,
  "conversion_data": {
    "original_amount": 250.00,
    "original_currency": "TND",
    "converted_amount": 75.76,
    "converted_currency": "EUR",
    "exchange_rate": 3.3
  }
}
```

## 🧮 Exchange Rate Examples

### Current Market Rates (approximate)
- **1 EUR = 3.3 TND** (correct)
- **1 TND = 0.303 EUR** (inverse)

### Conversion Examples
| TND Amount | Exchange Rate | EUR Amount | Stripe Amount (cents) |
|------------|---------------|------------|----------------------|
| 100.00 | 3.3 | 30.30 | 3030 |
| 250.00 | 3.3 | 75.76 | 7576 |
| 500.00 | 3.3 | 151.52 | 15152 |

## 🚫 Common Mistakes to Avoid

### ❌ Wrong: Double Multiplication
```php
// Frontend already multiplied by 100
$stripeAmount = $request->amount * 100; // WRONG: 25000 * 100 = 2,500,000
```

### ❌ Wrong: Using Frontend Amount Directly
```php
// Frontend amount is already converted, don't use it for conversion
$eurAmount = $request->amount / 100; // WRONG: This is already converted
```

### ❌ Wrong: Inverted Exchange Rate
```php
$exchangeRate = 1 / 3.3; // WRONG: This gives 0.303 (inverted)
$eurAmount = $originalAmount * $exchangeRate; // WRONG: 250 * 0.303 = 75.75
```

### ✅ Correct: Use Original Amount
```php
$exchangeRate = 3.3; // CORRECT: 1 EUR = 3.3 TND
$eurAmount = $originalAmount / $exchangeRate; // CORRECT: 250 / 3.3 = 75.76
$stripeAmount = round($eurAmount * 100); // CORRECT: 7576 cents
```

## 🔍 Debugging Checklist

### Verify These Values in Logs:
1. **Original amount**: Should be 250.00 (TND)
2. **Exchange rate**: Should be ~3.3 (not 0.3)
3. **Converted amount**: Should be ~75.76 (EUR)
4. **Stripe amount**: Should be ~7576 (cents)
5. **Final charge**: Should be ~€75.76 (not €7400)

### Log Template:
```php
Log::info('Payment conversion debug', [
    'step' => 'currency_conversion',
    'original_amount' => $originalAmount,
    'original_currency' => $originalCurrency,
    'exchange_rate' => $exchangeRate,
    'converted_eur_amount' => $eurAmount,
    'stripe_amount_cents' => $stripeAmount,
    'expected_charge' => '€' . number_format($eurAmount, 2)
]);
```

## 🧪 Test Cases

### Test Case 1: Standard Order
- **Input**: 250.00 TND
- **Expected**: €75.76 charge
- **Stripe Amount**: 7576 cents

### Test Case 2: Small Order
- **Input**: 50.00 TND  
- **Expected**: €15.15 charge
- **Stripe Amount**: 1515 cents

### Test Case 3: Large Order
- **Input**: 1000.00 TND
- **Expected**: €303.03 charge
- **Stripe Amount**: 30303 cents

## 📞 Support

If you need assistance implementing these changes or have questions about the currency conversion logic, please contact the frontend team for clarification.

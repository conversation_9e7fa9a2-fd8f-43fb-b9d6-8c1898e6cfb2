{"name": "material-tailwind-kit-react", "private": true, "version": "2.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "build:production": "NODE_ENV=production vite build", "preview": "vite preview", "optimize:images": "node scripts/optimize-images.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@heroicons/react": "2.0.18", "@material-tailwind/react": "^2.1.10", "axios": "^1.9.0", "bootstrap": "^5.3.3", "compression-webpack-plugin": "^11.1.0", "font-awesome": "^4.7.0", "keycloak-js": "^26.1.4", "lucide-react": "^0.477.0", "prop-types": "15.8.1", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-router-dom": "^6.30.0", "react-slick": "^0.30.3", "sass": "^1.85.1", "slick-carousel": "^1.8.1"}, "devDependencies": {"@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "18.2.31", "@types/react-dom": "18.2.14", "@vitejs/plugin-react": "4.1.0", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "autoprefixer": "10.4.16", "cypress": "^14.4.0", "jsdom": "^26.1.0", "lint-staged": "^15.5.1", "playwright": "^1.52.0", "postcss": "8.4.31", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "0.5.6", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "3.3.4", "terser": "^5.19.2", "vite": "^5.4.19", "vite-plugin-compression2": "^0.10.6", "vite-plugin-pwa": "^0.16.7", "vitest": "^3.1.4", "workbox-window": "^7.0.0"}}
import React, { useState } from 'react';
import { MessageCircle, X, Send, Sparkles, Home, Package, CreditCard, Truck } from 'lucide-react';

const Chatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [followUpQuestions, setFollowUpQuestions] = useState([]);
  const [isTyping, setIsTyping] = useState(false);

  const handleToggle = () => setIsOpen(!isOpen);

  const getRelatedQuestions = (question) => {
    const q = question.toLowerCase();

    if (q.includes("livraison")) {
      return ["Quels sont les frais de livraison ?"];
    }
    if (q.includes("paiement")) {
      return ["Est-ce que vous acceptez les chèques ?", "Le paiement est-il sécurisé ?"];
    }
    if (q.includes("retour")) {
      return ["Comment faire un retour ?", "Le retour est-il gratuit ?"];
    }
    if (q.includes("marque") && !q.includes("promotion")) {
      return [];
    }
    if (q.includes("catégorie")) {
      return ["Avez-vous une catégorie pour les luminaires ?", "Quelle est la catégorie la plus populaire ?"];
    }
    if (q.includes("produit")) {
      return ["Quels sont vos produits les plus vendus ?", "Puis-je voir les nouveautés ?"];
    }
    if (q.includes("bonjour")) {
      return ["Quels produits vendez-vous ?", "Comment puis-je vous contacter ?"];
    }
    return [];
  };

  const fetchDynamicResponse = async (keyword) => {
    try {
      // Simulation d'une API call pour la démo
      await new Promise(resolve => setTimeout(resolve, 800));
      if (keyword.includes('marque')) {
        // Appel API pour récupérer les marques
        try {
          const response = await fetch('https://laravel-api.fly.dev/api/marques');
          const data = await response.json();
          if (Array.isArray(data) && data.length > 0) {
            const marqueList = data.map(m => m.nom_marque || m.nom || m.name).filter(Boolean).join(', ');
            return 'Voici nos marques : ' + marqueList;
          } else {
            return 'Aucune marque trouvée.';
          }
        } catch (e) {
          return "Erreur lors de la récupération des marques.";
        }
      }
      if (keyword.includes('marque') && keyword.includes('promotion')) {
        return 'Marques avec promotions : Ikea, Maisons du Monde, Habitat, La Redoute';
      }
      if (keyword.includes('catégorie')) {
        // Appel API pour récupérer les catégories
        try {
          const response = await fetch('https://laravel-api.fly.dev/api/categories');
          const data = await response.json();
          if (Array.isArray(data) && data.length > 0) {
            const catList = data.map(c => c.nom_categorie || c.nom || c.name).filter(Boolean).join(', ');
            return 'Voici nos catégories : ' + catList;
          } else {
            return 'Aucune catégorie trouvée.';
          }
        } catch (e) {
          return "Erreur lors de la récupération des catégories.";
        }
      }
      if (keyword.includes('produit')) {
        return 'Voici quelques produits : Canapé Scandinave, Lampe Design, Coussin Velours, Miroir Doré...';
      }
      if (keyword.includes('quelles') && keyword.includes('catégor') && keyword.includes('disponible')) {
        try {
          const response = await fetch('https://laravel-api.fly.dev/api/categories');
          const data = await response.json();
          if (Array.isArray(data) && data.length > 0) {
            const catList = data.map(c => c.nom_categorie || c.nom || c.name).filter(Boolean).join(', ');
            return 'Voici les catégories disponibles : ' + catList;
          } else {
            return 'Aucune catégorie trouvée.';
          }
        } catch (e) {
          return "Erreur lors de la récupération des catégories.";
        }
      }
      return null;
    } catch (error) {
      return "Erreur lors de la récupération des données.";
    }
  };

  const getBotResponse = async (text) => {
    const lowerInput = text.toLowerCase();

    // Simuler un délai de frappe
    setIsTyping(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsTyping(false);

    if (lowerInput === 'quels sont les frais de livraison ?') {
      return "Les frais de livraison sont de 7 dinars pour toute commande.";
    } else if (lowerInput === 'est-ce que vous acceptez les chèques ?') {
      return "Oui, nous acceptons les chèques bancaires ainsi que tous les moyens de paiement sécurisés.";
    } else if (lowerInput === 'le paiement est-il sécurisé ?') {
      return "Absolument ! Le paiement est entièrement sécurisé par nos partenaires bancaires certifiés.";
    } else if (lowerInput.includes('livraison')) {
      return "La livraison prend généralement 3 à 5 jours ouvrables selon votre localisation.";
    } else if (lowerInput.includes('paiement')) {
      return "Nous acceptons les cartes bancaires, PayPal, et les virements bancaires.";
    } else if (lowerInput.includes('retour')) {
      return "Vous disposez de 14 jours pour retourner un produit en parfait état.";
    } else if (lowerInput.includes('déco')) {
      return "Nous proposons une collection exclusive d'objets de décoration tendance et intemporels.";
    } else if (lowerInput.includes('bonjour')) {
      return "Bonjour ! Je suis ravi de vous accueillir. Comment puis-je vous accompagner aujourd'hui ?";
    } else {
      const dynamicResponse = await fetchDynamicResponse(lowerInput);
      if (dynamicResponse) {
        return dynamicResponse;
      }
      return "Merci pour votre question ! Un de nos conseillers spécialisés vous répondra dans les plus brefs délais.";
    }
  };

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage = { sender: 'user', text: input };
    setMessages((prev) => [...prev, userMessage]);

    const botText = await getBotResponse(input);
    const botMessage = { sender: 'bot', text: botText };

    setMessages((prev) => [...prev, botMessage]);
    setFollowUpQuestions(getRelatedQuestions(input).slice(0, 2));
    setInput('');
  };

  const handleSuggestedClick = async (question) => {
    const userMessage = { sender: 'user', text: question };
    setMessages((prev) => [...prev, userMessage]);

    const botText = await getBotResponse(question);
    const botMessage = { sender: 'bot', text: botText };

    setMessages((prev) => [...prev, botMessage]);
    setFollowUpQuestions(getRelatedQuestions(question).slice(0, 2));
  };

  const suggestedQuestions = [
    { text: "Délais de livraison", icon: <Truck className="w-3 h-3" /> },
    { text: "Moyens de paiement", icon: <CreditCard className="w-3 h-3" /> },
    { text: "Nos marques", icon: <Sparkles className="w-3 h-3" /> },
    { text: "Nos catégories", icon: <Package className="w-3 h-3" /> },
    { text: "Quelles sont les catégories disponibles ?", icon: <Package className="w-3 h-3" /> },
    { text: "Nos produits", icon: <Home className="w-3 h-3" /> }
  ];

  return (
    <>
      {/* Styles globaux */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) scale(1); }
          50% { transform: translateY(-8px) scale(1.02); }
        }
        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeIn {
          from { opacity: 0; transform: scale(0.95); }
          to { opacity: 1; transform: scale(1); }
        }
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        @keyframes typing {
          0%, 60%, 100% { transform: translateY(0); }
          30% { transform: translateY(-10px); }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-fade-in-up { animation: fadeInUp 0.5s ease-out; }
        .animate-fade-in { animation: fadeIn 0.3s ease-out; }
        .animate-pulse-custom { animation: pulse 2s ease-in-out infinite; }
        .animate-typing { animation: typing 1.4s ease-in-out infinite; }
        .glass { 
          backdrop-filter: blur(16px); 
          -webkit-backdrop-filter: blur(16px);
          background: rgba(255, 255, 255, 0.9);
        }
        .scrollbar-hidden::-webkit-scrollbar { width: 4px; }
        .scrollbar-hidden::-webkit-scrollbar-track { background: transparent; }
        .scrollbar-hidden::-webkit-scrollbar-thumb { 
          background: rgba(166, 123, 91, 0.3); 
          border-radius: 2px; 
        }
        .scrollbar-hidden::-webkit-scrollbar-thumb:hover { 
          background: rgba(166, 123, 91, 0.5); 
        }
        .gradient-border {
          background: linear-gradient(135deg, #A67B5B, #FFD7B0);
          padding: 2px;
          border-radius: 50%;
        }
        .gradient-border-inner {
          background: white;
          border-radius: 50%;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>

      <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end">
        {/* Floating Button */}
        {!isOpen && (
          <div className="relative">
            <div className="gradient-border w-16 h-16 animate-float hover:scale-110 transition-all duration-300 cursor-pointer shadow-2xl" onClick={handleToggle}>
              <div className="gradient-border-inner">
                <MessageCircle className="w-7 h-7 text-[#A67B5B]" />
              </div>
            </div>
            {/* Notification badge */}
           
          </div>
        )}

        {/* Chatbot Window */}
        {isOpen && (
          <div className="w-[440px] max-w-[95vw] glass rounded-3xl shadow-2xl border border-white/30 flex flex-col overflow-hidden animate-fade-in-up backdrop-blur-xl">
            {/* Header */}
            <div className="relative bg-gradient-to-r from-[#A67B5B] via-[#B8896A] to-[#FFD7B0] text-white px-6 py-4 shadow-lg">
              <div className="absolute inset-0 bg-black/5"></div>
              <div className="relative flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg">Assistant Déco</h3>
                    <p className="text-white/80 text-sm">Conseiller en ligne</p>
                  </div>
                </div>
                <button 
                  onClick={handleToggle} 
                  className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-200 backdrop-blur-sm"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="px-4 py-3 bg-gradient-to-r from-[#f9f6f3] to-[#fff7f0] border-b border-[#A67B5B]/10">
              <div className="flex flex-wrap gap-2">
                {suggestedQuestions.slice(0, 4).map((item, idx) => (
                  <button
                    key={idx}
                    onClick={() => handleSuggestedClick(item.text)}
                    className="group flex items-center gap-2 bg-white/80 hover:bg-[#A67B5B] text-[#A67B5B] hover:text-white px-3 py-2 rounded-xl text-sm font-medium shadow-sm transition-all duration-300 border border-[#A67B5B]/20 hover:scale-105 hover:shadow-md"
                  >
                    <span className="group-hover:scale-110 transition-transform duration-200">
                      {item.icon}
                    </span>
                    {item.text}
                  </button>
                ))}
              </div>
            </div>

            {/* Messages Container */}
            <div className="flex-1 px-4 py-4 space-y-4 overflow-y-auto bg-gradient-to-b from-[#f9f6f3] to-[#fff7f0] scrollbar-hidden" style={{ maxHeight: 380 }}>
              {messages.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gradient-to-br from-[#A67B5B]/10 to-[#FFD7B0]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-8 h-8 text-[#A67B5B]/60" />
                  </div>
                  <h4 className="text-[#A67B5B] font-semibold text-lg mb-2">Bienvenue !</h4>
                  <p className="text-gray-500 text-sm">Comment puis-je vous aider avec votre décoration ?</p>
                </div>
              )}

              {messages.map((msg, index) => (
                <div key={index} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in`}>
                  <div className={`max-w-[85%] ${msg.sender === 'user' ? 'order-1' : 'order-2'}`}>
                    <div className={`rounded-2xl px-4 py-3 shadow-lg text-sm font-medium leading-relaxed ${
                      msg.sender === 'user'
                        ? 'bg-gradient-to-br from-[#A67B5B] to-[#B8896A] text-white rounded-br-md'
                        : 'bg-white text-[#4A4A4A] border border-[#A67B5B]/10 rounded-bl-md shadow-xl'
                    }`}>
                      {msg.text}
                    </div>
                  </div>
                  {msg.sender === 'bot' && (
                    <div className="w-8 h-8 bg-gradient-to-br from-[#A67B5B] to-[#FFD7B0] rounded-full flex items-center justify-center mr-2 mt-auto mb-1 order-1 shadow-md">
                      <Sparkles className="w-4 h-4 text-white" />
                    </div>
                  )}
                </div>
              ))}

              {/* Typing Indicator */}
              {isTyping && (
                <div className="flex justify-start animate-fade-in">
                  <div className="w-8 h-8 bg-gradient-to-br from-[#A67B5B] to-[#FFD7B0] rounded-full flex items-center justify-center mr-2 shadow-md">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <div className="bg-white rounded-2xl rounded-bl-md px-4 py-3 shadow-lg border border-[#A67B5B]/10">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-[#A67B5B]/40 rounded-full animate-typing"></div>
                      <div className="w-2 h-2 bg-[#A67B5B]/40 rounded-full animate-typing" style={{animationDelay: '0.2s'}}></div>
                      <div className="w-2 h-2 bg-[#A67B5B]/40 rounded-full animate-typing" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Follow-up Questions */}
            {followUpQuestions.length > 0 && (
              <div className="px-4 py-3 bg-white/50 border-t border-[#A67B5B]/10 backdrop-blur-sm">
                <p className="text-xs text-[#A67B5B]/80 mb-2 font-medium">Questions suggérées :</p>
                <div className="flex flex-wrap gap-2">
                  {followUpQuestions.map((q, idx) => (
                    <button
                      key={idx}
                      onClick={() => handleSuggestedClick(q)}
                      className="bg-gradient-to-r from-[#FFD7B0] to-[#A67B5B] text-white px-3 py-1.5 rounded-full text-xs font-medium hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                      {q}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default Chatbot;
import React from 'react';

const PaymentDebugInfo = ({ 
  originalAmount, 
  originalCurrency, 
  processedAmount, 
  targetCurrency, 
  conversionData 
}) => {
  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <h4 className="text-sm font-medium text-yellow-800 mb-2">
        🔧 Debug Info (Development Only)
      </h4>
      <div className="text-xs text-yellow-700 space-y-1">
        <div>
          <strong>Original:</strong> {originalAmount} {originalCurrency}
        </div>
        <div>
          <strong>Processed:</strong> {processedAmount} {targetCurrency} 
          <span className="text-gray-500 ml-1">
            (x100 = {processedAmount * 100} smallest units)
          </span>
        </div>
        {conversionData && (
          <>
            <div>
              <strong>Exchange Rate:</strong> 1 {conversionData.converted_currency?.toUpperCase()} = {conversionData.exchange_rate} {conversionData.original_currency?.toUpperCase()}
            </div>
            <div>
              <strong>Converted Amount:</strong> {conversionData.converted_amount} {conversionData.converted_currency?.toUpperCase()}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentDebugInfo;

import React, { useState, useEffect } from 'react';
import { Elements, useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { stripePromise, STRIPE_CONFIG } from '../Services/stripe.config';
import LoadingSpinner from './LoadingSpinner';
import paymentService from '../Services/payment.service';
import { PAYMENT_STATUS } from '../Services/stripe.config';
import PaymentDebugInfo from './PaymentDebugInfo';

// Inner payment form component that uses Stripe hooks
const StripePaymentFormInner = ({
  amount,
  currency = 'eur',
  orderId,
  orderData,
  onSuccess,
  onCancel,
  conversionData
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      // Confirm the payment with Stripe
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment/success`,
        },
        redirect: 'if_required'
      });

      if (error) {
        // Payment failed
        setErrorMessage(error.message);
        console.error('Payment error:', error);
      } else if (paymentIntent) {
        // Handle different payment statuses
        switch (paymentIntent.status) {
          case PAYMENT_STATUS.SUCCEEDED:
            // Payment succeeded
            console.log('Payment succeeded:', paymentIntent);
            if (onSuccess) {
              onSuccess({
                paymentIntentId: paymentIntent.id,
                status: paymentIntent.status,
                amount: paymentIntent.amount,
                currency: paymentIntent.currency
              });
            }
            break;
          case PAYMENT_STATUS.PROCESSING:
            // Payment is processing - show success but note processing
            console.log('Payment processing:', paymentIntent);
            if (onSuccess) {
              onSuccess({
                paymentIntentId: paymentIntent.id,
                status: paymentIntent.status,
                amount: paymentIntent.amount,
                currency: paymentIntent.currency,
                processing: true
              });
            }
            break;
          case PAYMENT_STATUS.REQUIRES_ACTION:
            // Additional action required - handled by Stripe
            console.log('Payment requires action:', paymentIntent);
            break;
          default:
            setErrorMessage('Statut de paiement inattendu. Veuillez contacter le support.');
            console.error('Unexpected payment status:', paymentIntent.status);
        }
      }
    } catch (error) {
      console.error('Payment submission error:', error);
      setErrorMessage('Erreur lors du traitement du paiement');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg">
      {/* Debug Info (Development Only) */}
      <PaymentDebugInfo
        originalAmount={orderData?.originalAmount || amount}
        originalCurrency={orderData?.originalCurrency || currency.toUpperCase()}
        processedAmount={amount}
        targetCurrency={currency.toUpperCase()}
        conversionData={conversionData}
      />

      {/* Payment Amount Display */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Résumé du paiement</h3>

        {conversionData ? (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Montant original:</span>
              <span className="font-medium">{conversionData.original_amount.toFixed(3)} TND</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Taux de change:</span>
              <span className="font-medium">1 EUR = {conversionData.exchange_rate.toFixed(3)} TND</span>
            </div>
            <div className="border-t pt-2 flex justify-between">
              <span className="font-semibold text-gray-900">Montant à payer:</span>
              <span className="font-bold text-lg text-[#A67B5B]">
                €{conversionData.converted_amount.toFixed(2)}
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Votre paiement sera traité en EUR. Le montant affiché inclut la conversion de devise.
            </p>
          </div>
        ) : (
          <div className="flex justify-between">
            <span className="font-semibold text-gray-900">Montant total:</span>
            <span className="font-bold text-lg text-[#A67B5B]">
              {currency === 'eur' ? '€' : currency.toUpperCase()}{amount?.toFixed(2)}
            </span>
          </div>
        )}
      </div>

      {/* Security Notice */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Paiement sécurisé
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              Vos informations de paiement sont protégées par le chiffrement SSL et traitées de manière sécurisée par Stripe.
            </div>
          </div>
        </div>
      </div>

      {/* Payment Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Informations de paiement</h3>

          <div className="border border-gray-200 rounded-lg p-4">
            <PaymentElement
              id="payment-element"
              options={{
                layout: 'tabs'
              }}
            />
          </div>
        </div>

        {/* Error Message */}
        {errorMessage && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Erreur de paiement
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  {errorMessage}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#A67B5B] transition-colors"
          >
            Annuler
          </button>
          <button
            type="submit"
            disabled={!stripe || isLoading}
            className={`flex-1 flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              !stripe || isLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#A67B5B] hover:bg-[#8B6B4F] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#A67B5B]'
            } transition-colors`}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="sm" />
                <span className="ml-2">Traitement en cours...</span>
              </>
            ) : (
              `Payer ${conversionData ? `€${conversionData.converted_amount.toFixed(2)}` : (currency === 'eur' ? '€' : currency.toUpperCase()) + amount?.toFixed(2)}`
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

// Main wrapper component that handles payment intent creation and Elements provider
const StripePaymentForm = ({
  amount,
  currency = 'eur',
  orderId,
  orderData,
  onSuccess,
  onCancel
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [paymentIntentId, setPaymentIntentId] = useState('');
  const [conversionData, setConversionData] = useState(null);

  useEffect(() => {
    // Create Payment Intent when component mounts
    const createPaymentIntent = async () => {
      try {
        setIsLoading(true);
        setErrorMessage('');

        // Send the amount in the smallest currency unit (cents for EUR, millimes for TND)
        // The backend will handle currency conversion from TND to EUR if needed
        const paymentData = {
          amount: Math.round(amount * 100), // Convert to smallest unit
          currency: currency.toLowerCase(),
          commande_id: orderId, // Include the order ID
          customer_email: orderData?.email,
          shipping_address: orderData?.shipping_address,
          metadata: {
            customer_name: orderData?.customerName || 'Client',
            items_count: orderData?.itemsCount || 0,
            order_id: orderId
          }
        };

        // If we have original currency info (TND), include it for backend conversion
        if (orderData?.originalCurrency && orderData?.originalAmount) {
          paymentData.original_currency = orderData.originalCurrency;
          paymentData.original_amount = orderData.originalAmount;
        }

        const response = await paymentService.createPaymentIntent(paymentData);

        if (response.status === 'success') {
          setClientSecret(response.client_secret);
          setPaymentIntentId(response.payment_intent_id);

          // Handle currency conversion data if present
          if (response.conversion_data) {
            setConversionData(response.conversion_data);
            console.log('Currency conversion applied:', response.conversion_data);
          }
        } else {
          setErrorMessage(response.message || 'Échec de l\'initialisation du paiement');
        }
      } catch (error) {
        console.error('Payment initialization error:', error);
        setErrorMessage('Erreur réseau lors de l\'initialisation du paiement');
      } finally {
        setIsLoading(false);
      }
    };

    if (amount && amount > 0 && orderId) {
      createPaymentIntent();
    } else if (!orderId) {
      setErrorMessage('ID de commande requis pour le paiement');
    }
  }, [amount, currency, orderId]);

  // Show loading state while creating payment intent
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600">Initialisation du paiement sécurisé...</p>
      </div>
    );
  }

  // Show error state if payment intent creation failed
  if (errorMessage) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Erreur d'initialisation du paiement
            </h3>
            <div className="mt-2 text-sm text-red-700">
              {errorMessage}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Only render the Elements provider when we have a clientSecret
  if (!clientSecret) {
    return null;
  }

  const options = {
    clientSecret,
    appearance: STRIPE_CONFIG.appearance,
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      <StripePaymentFormInner
        amount={amount}
        currency={currency}
        orderId={orderId}
        orderData={orderData}
        onSuccess={onSuccess}
        onCancel={onCancel}
        conversionData={conversionData}
      />
    </Elements>
  );
};

export default StripePaymentForm;

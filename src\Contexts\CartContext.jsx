import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import { useAuth } from './AuthContext';
import cartService from '../Services/cart.service';
import { keycloak } from '../Services/keycloakInstance';
import debouncedRefresh, { smartRefresh } from '../utils/debouncedRefresh';
import performanceMonitor from '../utils/performanceMonitor';

// Create the context
const CartContext = createContext();

// Custom hook to use the cart context
export const useCart = () => useContext(CartContext);

// Provider component
// Create a base64 encoded SVG placeholder to avoid external requests
const PLACEHOLDER_IMAGE = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2YxZjFmMSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTkiPkltYWdlIGluZGlzcG9uaWJsZTwvdGV4dD48L3N2Zz4=';

// Helper function to create a cart object from local items
const createLocalCartObject = (items) => {
  return {
    items: items.map(item => {
      // Ensure item.id is a string for concatenation
      const itemId = item.id ? item.id.toString() : '0';
      const variantId = item.variante_id ? item.variante_id.toString() : '0';

      return {
        id: `local_${itemId}_${variantId}`,
        produit: {
          id: item.id,
          nom: item.nom_produit || item.produit?.nom || 'Produit',
          // Use embedded SVG placeholder instead of URL to prevent infinite requests
          image: item.image_produit || item.produit?.image || PLACEHOLDER_IMAGE,
          prix: item.prix_produit || item.produit?.prix || 0
        },
        variante: item.variante_id ? {
          id: item.variante_id,
          attributs: item.variante_attributs || []
        } : null,
        quantite: item.quantite || 1,
        prix_unitaire: parseFloat(item.prix_produit || item.produit?.prix || 0),
        prix_total: parseFloat(item.prix_produit || item.produit?.prix || 0) * (item.quantite || 1)
      };
    }),
    nombre_items: items.reduce((total, item) => total + (item.quantite || 1), 0),
    sous_total: items.reduce((total, item) => {
      const price = parseFloat(item.prix_produit || item.produit?.prix || 0);
      return total + (price * (item.quantite || 1));
    }, 0),
    total: items.reduce((total, item) => {
      const price = parseFloat(item.prix_produit || item.produit?.prix || 0);
      return total + (price * (item.quantite || 1));
    }, 0)
  };
};

export const CartProvider = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [cart, setCart] = useState({ items: [], nombre_items: 0, sous_total: 0, total: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastSyncTime, setLastSyncTime] = useState(Date.now());
  // Create a ref to store previous auth state
  const prevAuthStateRef = useRef({ isAuthenticated, userId: user?.id });

  // Helper function to trigger cart sync between tabs - optimized version
  const triggerCartSync = (cartData = null) => {
    // Only trigger sync if we have cart data
    if (cartData) {
      // Use a debounced approach to reduce frequent updates
      const now = Date.now();
      // Only sync if it's been more than 2 seconds since last sync
      if (now - lastSyncTime > 2000) {
        // Store sync info in localStorage to trigger storage event in other tabs
        localStorage.setItem('cart_sync', JSON.stringify({
          action: 'update',
          timestamp: now
        }));

        // Update shared cart storage
        if (isAuthenticated) {
          localStorage.setItem('shared_cart', JSON.stringify(cartData));
        } else {
          sessionStorage.setItem('shared_cart', JSON.stringify(cartData));
        }

        // Update our own last sync time
        setLastSyncTime(now);
      }
    }
  };

  // Smart refresh cart from server with performance optimization
  const refreshCart = async (force = false) => {
    if (!isAuthenticated) {
      return false;
    }

    const refreshFn = async () => {
      try {
        setLoading(true);
        const startTime = Date.now();

        const response = await cartService.getCart();

        if (response.status === 'success') {
          setCart({
            ...response.data,
            last_updated: Date.now()
          });

          // Update shared cart for cross-browser sync
          triggerCartSync(response.data);

          // Record performance metrics
          performanceMonitor.recordCall('cart', '/cart', Date.now() - startTime, true);
          return true;
        }

        performanceMonitor.recordCall('cart', '/cart', Date.now() - startTime, false);
        return false;
      } catch (err) {
        setError('Failed to refresh cart data');
        performanceMonitor.recordCall('cart', '/cart', Date.now() - startTime, false);
        return false;
      } finally {
        setLoading(false);
      }
    };

    if (force) {
      return await debouncedRefresh.forceRefresh('cart', refreshFn);
    } else {
      smartRefresh('cart', refreshFn, {
        delay: 1000,
        maxAge: 5 * 60 * 1000, // 5 minutes
        onlyWhenVisible: true,
        onlyWhenActive: true
      });
      return true;
    }
  };

  // Listen for storage events to sync cart between tabs
  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === 'cart_sync') {
        const syncData = JSON.parse(event.newValue || '{}');

        // Only process if this is a newer sync than our last one
        if (syncData.timestamp > lastSyncTime) {
          setLastSyncTime(syncData.timestamp);

          // Refresh cart from server if we're authenticated
          if (isAuthenticated) {
            // Use our refreshCart function to ensure consistent behavior
            refreshCart().catch(err => {
              // Fallback to direct API call if refreshCart fails
              cartService.getCart().then(response => {
                if (response.status === 'success' && response.data) {
                  setCart(response.data);
                }
              }).catch(() => {
                // Silent fail
              });
            });
          } else {
            // For unauthenticated users, check if there's a shared cart
            const sharedCart = JSON.parse(localStorage.getItem('shared_cart'));
            if (sharedCart) {
              setCart(sharedCart);
            }
          }
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [isAuthenticated, lastSyncTime]);

  // Handle authentication state changes
  useEffect(() => {
    // Function to handle auth state changes
    const handleAuthStateChange = async () => {
      // Case 1: User logged out
      if (prevAuthStateRef.current.isAuthenticated && !isAuthenticated) {

        // Get the current cart before clearing
        const currentCart = { ...cart };

        // Clear any user-specific cart data
        if (prevAuthStateRef.current.userId) {
          localStorage.removeItem(`cart_user_${prevAuthStateRef.current.userId}`);
        }

        // Convert the authenticated cart to local format
        if (currentCart.items && currentCart.items.length > 0) {
          // Convert cart items to local format
          const localItems = currentCart.items.map(item => ({
            id: item.produit.id,
            nom_produit: item.produit.nom,
            prix_produit: item.prix_unitaire,
            image_produit: item.produit.image,
            quantite: item.quantite,
            variante_id: item.variante?.id || null,
            variante_attributs: item.variante?.attributs || []
          }));

          // Save to localStorage
          localStorage.setItem('cart', JSON.stringify(localItems));

          // Create updated cart object with local IDs
          const localCartObj = createLocalCartObject(localItems);
          setCart(localCartObj);

          // Also update shared cart
          triggerCartSync(localCartObj);
        } else {
          // Reset to empty cart first
          const emptyCart = { items: [], nombre_items: 0, sous_total: 0, total: 0 };
          setCart(emptyCart);

          // Then check for guest cart data
          const localCart = JSON.parse(localStorage.getItem('cart')) || [];
          if (localCart.length > 0) {
            const localCartObj = createLocalCartObject(localCart);
            setCart(localCartObj);
          }
        }
      }

      // Case 2: User logged in
      if (!prevAuthStateRef.current.isAuthenticated && isAuthenticated) {
        // Check if we have local cart items to merge with server
        const localCart = JSON.parse(localStorage.getItem('cart')) || [];
        const hasLocalItems = localCart.length > 0;

        // Force refresh cart from server
        try {
          // First get the current server cart
          const response = await cartService.getCart();

          if (response.status === 'success') {
            // Check if server cart is empty and we have local items
            if (response.data.items.length === 0 && hasLocalItems) {

              try {
                // Create a guest ID for merging
                // We'll use a UUID format as required by the updated API
                // The mergeCart function will generate a UUID internally

                // Merge local cart with server using a generated UUID
                const mergeResponse = await cartService.mergeCart();

                if (mergeResponse.status === 'success') {
                  // Get the updated cart from server
                  const refreshResponse = await cartService.getCart();

                  if (refreshResponse.status === 'success') {
                    setCart(refreshResponse.data);

                    // Clear local cart after successful merge
                    localStorage.removeItem('cart');
                  } else {
                    // Use merge response if refresh fails
                    setCart(mergeResponse.data);
                  }
                } else {
                  // Use server cart if merge fails
                  setCart(response.data);
                }
              } catch (mergeError) {
                console.error('Error merging local cart with server:', mergeError);
                // Use server cart if merge fails
                setCart(response.data);
              }
            } else {
              // Server cart has items or no local items, use server cart
              setCart(response.data);

              // If we have local items, try to merge them with server
              if (hasLocalItems && response.data.items.length > 0) {

                try {
                  // Create a guest ID for merging
                  // We'll use a UUID format as required by the updated API
                  // The mergeCart function will generate a UUID internally

                  // Merge local cart with server using a generated UUID
                  await cartService.mergeCart();

                  // Get the updated cart from server
                  const refreshResponse = await cartService.getCart();

                  if (refreshResponse.status === 'success') {
                    setCart(refreshResponse.data);

                    // Clear local cart after successful merge
                    localStorage.removeItem('cart');
                  }
                } catch (mergeError) {
                  console.error('Error merging local cart with server:', mergeError);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error loading user cart after login:', error);
        }
      }

      // Case 3: User changed (switched accounts)
      if (prevAuthStateRef.current.isAuthenticated && isAuthenticated &&
          prevAuthStateRef.current.userId !== user?.id) {

        // Force refresh cart from server
        try {
          const response = await cartService.getCart();
          if (response.status === 'success') {
            setCart(response.data);
          }
        } catch (error) {
          // Silent fail
        }
      }

      // Update previous state reference
      prevAuthStateRef.current = { isAuthenticated, userId: user?.id };
    };

    // Call the handler when auth state changes
    handleAuthStateChange();

  }, [isAuthenticated, user?.id]);


  // Ref to track if we've already loaded the cart to prevent multiple loads
  const hasLoadedCartRef = useRef(false);

  // Load cart data from API or localStorage
  useEffect(() => {
    const loadCart = async () => {
      // Skip if we've already loaded the cart
      if (hasLoadedCartRef.current) {
        return;
      }

      setLoading(true);
      try {
        // Mark as loaded to prevent duplicate loads
        hasLoadedCartRef.current = true;

        // Check for a shared cart in storage
        const sharedCartKey = 'shared_cart';
        // For authenticated users, check localStorage, for unauthenticated users, check sessionStorage first
        const sharedCart = isAuthenticated
          ? JSON.parse(localStorage.getItem(sharedCartKey)) || null
          : JSON.parse(sessionStorage.getItem(sharedCartKey)) || null;

        if (isAuthenticated) {
          // If user is authenticated, try to get cart from API
          const response = await cartService.getCart();

          // Check if the API cart is empty or has items
          const hasApiItems = response.status === 'success' &&
                             response.data &&
                             response.data.items &&
                             response.data.items.length > 0;

          // Check if we have a shared cart with items
          const hasSharedItems = sharedCart &&
                                sharedCart.items &&
                                sharedCart.items.length > 0;

          // Get any user-specific local cart items
          // Use Keycloak user ID if available, otherwise fall back to user.id
          const userId = keycloak?.tokenParsed?.sub || user?.id;
          const localCartKey = `cart_user_${userId}`;
          const localCart = JSON.parse(localStorage.getItem(localCartKey)) || [];
          const hasLocalItems = localCart.length > 0;

          if (hasApiItems) {
            // API cart has items, use it as the primary source
            setCart(response.data);

            // If we also have local or shared items, try to merge them
            if (hasLocalItems || hasSharedItems) {

              // Combine local and shared items for merging
              const itemsToMerge = [
                ...localCart,
                ...(hasSharedItems ? sharedCart.items.map(item => ({
                  id: item.produit.id,
                  variante_id: item.variante?.id || null,
                  quantite: item.quantite,
                  nom_produit: item.produit.nom,
                  prix_produit: item.prix_unitaire,
                  image_produit: item.produit.image || PLACEHOLDER_IMAGE
                })) : [])
              ];

              if (itemsToMerge.length > 0) {
                try {
                  // Create a guest ID for merging
                  // We'll use a UUID format as required by the updated API
                  // The mergeCart function will generate a UUID internally

                  // Merge local cart with server using a generated UUID
                  await cartService.mergeCart();

                  // Clear local storage after successful merge
                  localStorage.removeItem(localCartKey);
                  if (hasSharedItems) localStorage.removeItem(sharedCartKey);

                  // Refresh cart from server
                  const refreshedResponse = await cartService.getCart();
                  if (refreshedResponse.status === 'success' && refreshedResponse.data) {
                    setCart(refreshedResponse.data);
                  }
                } catch (mergeError) {
                  console.error('Error merging items with API cart:', mergeError);
                }
              }
            }
          } else {
            // API cart is empty, check for local or shared items
            if (hasLocalItems || hasSharedItems) {
              // We have local or shared items, try to use them

              // Combine local and shared items
              const combinedItems = [
                ...localCart,
                ...(hasSharedItems ? sharedCart.items.map(item => ({
                  id: item.produit.id,
                  variante_id: item.variante?.id || null,
                  quantite: item.quantite,
                  nom_produit: item.produit.nom,
                  prix_produit: item.prix_unitaire,
                  image_produit: item.produit.image || PLACEHOLDER_IMAGE
                })) : [])
              ];

              // Try to merge with server
              try {
                // Create a guest ID for merging
                // We'll use a UUID format as required by the updated API
                // The mergeCart function will generate a UUID internally

                // Merge local cart with server using a generated UUID
                const mergeResponse = await cartService.mergeCart();

                // Clear localStorage after successful merge
                localStorage.removeItem(localCartKey);
                if (hasSharedItems) localStorage.removeItem(sharedCartKey);

                // Refresh cart from server
                const refreshedResponse = await cartService.getCart();
                if (refreshedResponse.status === 'success' &&
                    refreshedResponse.data &&
                    refreshedResponse.data.items &&
                    refreshedResponse.data.items.length > 0) {
                  // Server now has items, use it
                  setCart(refreshedResponse.data);
                } else {
                  // Server still empty, use local representation

                  // Create a cart object from combined items
                  const localCartObj = createLocalCartObject(combinedItems);
                  setCart(localCartObj);

                  // Save as shared cart for cross-browser access
                  localStorage.setItem(sharedCartKey, JSON.stringify(localCartObj));
                }
              } catch (mergeError) {
                // Silent fail

                // Create a cart object from combined items
                const localCartObj = createLocalCartObject(combinedItems);
                setCart(localCartObj);

                // Save as shared cart for cross-browser access
                localStorage.setItem(sharedCartKey, JSON.stringify(localCartObj));
              }
            } else {
              // No items anywhere, use empty API cart
              setCart(response.data || { items: [], nombre_items: 0, sous_total: 0, total: 0 });
            }
          }
        } else {
          // For unauthenticated users, check both regular cart and shared cart
          const localCart = JSON.parse(sessionStorage.getItem('cart')) || [];

          // Check if we have a shared cart with items
          const hasSharedItems = sharedCart &&
                                sharedCart.items &&
                                sharedCart.items.length > 0;

          if (hasSharedItems) {
            if (localCart.length > 0) {
              // We have both local and shared items, merge them

              // Convert shared items to local format for merging
              const sharedItemsLocal = sharedCart.items.map(item => ({
                id: item.produit.id,
                variante_id: item.variante?.id || null,
                quantite: item.quantite,
                nom_produit: item.produit.nom,
                prix_produit: item.prix_unitaire,
                image_produit: item.produit.image || PLACEHOLDER_IMAGE
              }));

              // To prevent duplicate merging, we'll check if the carts are already merged

              // Create a simple hash of the cart contents to compare
              const getCartHash = (items) => {
                return items
                  .map(item => `${item.id}_${item.variante_id || 0}_${item.quantite}`)
                  .sort()
                  .join('|');
              };

              const localCartHash = getCartHash(localCart);

              // Convert shared items to a comparable format
              const sharedItemsForHash = sharedItemsLocal.map(item => ({
                id: item.id,
                variante_id: item.variante_id,
                quantite: item.quantite
              }));

              const sharedCartHash = getCartHash(sharedItemsForHash);

              // Check if the carts are identical (already merged)
              const cartsAreIdentical = localCartHash === sharedCartHash;

              if (cartsAreIdentical) {

                // Create cart object from local cart only
                const localCartObj = createLocalCartObject(localCart);
                setCart(localCartObj);

                // Save to shared cart for consistency
                localStorage.setItem('shared_cart', JSON.stringify(localCartObj));
              } else {

                // Check for duplicates and merge quantities
                const mergedItems = [...localCart];

                sharedItemsLocal.forEach(sharedItem => {
                  const existingIndex = mergedItems.findIndex(item =>
                    item.id === sharedItem.id &&
                    (sharedItem.variante_id ? item.variante_id === sharedItem.variante_id : !item.variante_id)
                  );

                  if (existingIndex >= 0) {
                    // Item exists, don't add quantities to prevent duplication
                    // Just keep the existing quantity to avoid any increases
                  } else {
                    // New item, add to array
                    mergedItems.push(sharedItem);
                  }
                });

                // Create cart object from merged items
                const mergedCart = createLocalCartObject(mergedItems);
                setCart(mergedCart);

                // Save merged cart to both storages
                localStorage.setItem('cart', JSON.stringify(mergedItems));
                localStorage.setItem('shared_cart', JSON.stringify(mergedCart));
              }
            } else {
              // Only have shared items, use them
              setCart(sharedCart);
            }
          } else if (localCart.length > 0) {
            // Only have local items, use them and create a shared cart
            const localCartObj = createLocalCartObject(localCart);
            setCart(localCartObj);

            // Save as shared cart for cross-browser access
            localStorage.setItem('shared_cart', JSON.stringify(localCartObj));
          } else {
            // No items anywhere, use empty cart
            setCart({ items: [], nombre_items: 0, sous_total: 0, total: 0 });
          }
        }
      } catch (err) {
        setError('Failed to load cart data');

        // Fallback to localStorage in case of API error
        // Use Keycloak user ID if available, otherwise fall back to user.id
        const userId = keycloak?.tokenParsed?.sub || user?.id;
        const storageKey = isAuthenticated && userId ? `cart_user_${userId}` : 'cart';
        const localCart = JSON.parse(localStorage.getItem(storageKey)) || [];
        setCart({
          items: localCart.map(item => ({
            id: `local_${item.id}_${item.variante_id || 0}`,
            produit: {
              id: item.id,
              nom: item.nom_produit,
              image: item.image_produit || PLACEHOLDER_IMAGE,
              prix: item.prix_produit
            },
            variante: item.variante_id ? {
              id: item.variante_id,
              attributs: item.variante_attributs || []
            } : null,
            quantite: item.quantite,
            prix_unitaire: item.prix_produit,
            prix_total: item.prix_produit * item.quantite
          })),
          nombre_items: localCart.reduce((total, item) => total + item.quantite, 0),
          sous_total: localCart.reduce((total, item) => total + (item.prix_produit * item.quantite), 0),
          total: localCart.reduce((total, item) => total + (item.prix_produit * item.quantite), 0)
        });
      } finally {
        setLoading(false);
      }
    };

    // Only load cart when authentication state is determined
    if (isAuthenticated !== undefined) {
      loadCart();
    }

    // Set up a smart refresh system for authenticated users
    let refreshInterval;
    let visibilityChangeHandler;

    if (isAuthenticated && user) {
      // Only refresh when page becomes visible and it's been a while since last update
      visibilityChangeHandler = () => {
        if (!document.hidden) {
          const now = Date.now();
          const lastUpdate = cart?.last_updated || 0;

          // Only refresh if it's been more than 5 minutes since last update
          if (now - lastUpdate > 5 * 60 * 1000) {
            // Use requestIdleCallback to avoid blocking other operations
            if (window.requestIdleCallback) {
              window.requestIdleCallback(async () => {
                try {
                  const response = await cartService.getCart();
                  if (response.status === 'success' && response.data) {
                    setCart({
                      ...response.data,
                      last_updated: now
                    });

                    if (user?.id) {
                      localStorage.setItem(`cart_user_${user.id}`, JSON.stringify(response.data));
                    }
                    triggerCartSync(response.data);
                  }
                } catch (err) {
                  // Silent fail
                }
              });
            }
          }
        }
      };

      document.addEventListener('visibilitychange', visibilityChangeHandler);

      // Fallback: Less frequent polling (every 10 minutes instead of 30 seconds)
      refreshInterval = setInterval(async () => {
        // Only refresh if page is visible and user is active
        if (!document.hidden) {
          try {
            const response = await cartService.getCart();
            if (response.status === 'success' && response.data) {
              setCart({
                ...response.data,
                last_updated: Date.now()
              });

              if (user?.id) {
                localStorage.setItem(`cart_user_${user.id}`, JSON.stringify(response.data));
              }
              triggerCartSync(response.data);
            }
          } catch (err) {
            // Silent fail
          }
        }
      }, 10 * 60 * 1000); // Refresh every 10 minutes instead of 30 seconds
    }

    return () => {
      if (refreshInterval) clearInterval(refreshInterval);
      if (visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', visibilityChangeHandler);
      }
    };
  }, [isAuthenticated, user]);

  // Add item to cart with optimistic updates
  const addToCart = async (product, variant = null, quantity = 1, replaceQuantity = true) => {
    try {
      setLoading(true);

      // Optimistic update for immediate UI feedback
      const optimisticItem = {
        id: `temp_${Date.now()}`,
        produit: product,
        variante: variant,
        quantite: quantity,
        prix_unitaire: variant?.prix || product.prix,
        total: (variant?.prix || product.prix) * quantity
      };

      // Update cart immediately for better UX
      setCart(prevCart => {
        const newItems = [...(prevCart?.items || [])];

        // Check if item already exists
        const existingIndex = newItems.findIndex(item =>
          item.produit.id === product.id &&
          (variant ? item.variante?.id === variant.id : !item.variante)
        );

        if (existingIndex >= 0) {
          // Update existing item
          if (replaceQuantity) {
            newItems[existingIndex].quantite = quantity;
          } else {
            newItems[existingIndex].quantite += quantity;
          }
          newItems[existingIndex].total = newItems[existingIndex].prix_unitaire * newItems[existingIndex].quantite;
        } else {
          // Add new item
          newItems.push(optimisticItem);
        }

        const newTotal = newItems.reduce((sum, item) => sum + item.total, 0);

        return {
          ...prevCart,
          items: newItems,
          total: newTotal,
          last_updated: Date.now()
        };
      });

      // Perform actual API call in background without blocking UI
      if (isAuthenticated) {
        // Use setTimeout to make it truly non-blocking
        setTimeout(async () => {
          try {
            const response = await cartService.addToCart(
              product.id,
              variant?.id || null,
              quantity,
              replaceQuantity
            );

            if (response.status === 'success') {
              // Update with real data from server
              setCart({
                ...response.data,
                last_updated: Date.now()
              });

              if (user?.id) {
                localStorage.setItem(`cart_user_${user.id}`, JSON.stringify(response.data));
              }
              triggerCartSync(response.data);
            } else {
              // Revert optimistic update on failure
              refreshCart();
            }
          } catch (error) {
            // Revert optimistic update on error
            refreshCart();
          }
        }, 0);
      } else {
        // Use sessionStorage for unauthenticated users (temporary storage)
        const localCart = JSON.parse(sessionStorage.getItem('cart')) || [];

        // Check if product already exists in cart
        const existingItemIndex = localCart.findIndex(item =>
          item.id === product.id &&
          (variant ? item.variante_id === variant.id : !item.variante_id)
        );

        if (existingItemIndex >= 0) {
          // Update quantity if item exists
          if (replaceQuantity) {
            localCart[existingItemIndex].quantite = quantity;
          } else {
            localCart[existingItemIndex].quantite += quantity;
          }
        } else {
          // Add new item if it doesn't exist
          localCart.push({
            id: product.id,
            nom_produit: product.nom_produit || product.nom,
            prix_produit: variant?.prix_supplement
              ? (product.prix_produit || product.prix) + variant.prix_supplement
              : (product.prix_produit || product.prix),
            image_produit: product.image_produit || product.image || PLACEHOLDER_IMAGE,
            quantite: quantity,
            variante_id: variant?.id || null,
            variante_sku: variant?.sku || null,
            variante_attributs: variant?.attributs || []
          });
        }

        // Save to sessionStorage (temporary storage)
        sessionStorage.setItem('cart', JSON.stringify(localCart));

        // Create updated cart object
        const updatedCart = createLocalCartObject(localCart);

        // Update state
        setCart(updatedCart);

        // Update shared cart
        localStorage.setItem('shared_cart', JSON.stringify(updatedCart));
      }
    } catch (err) {
      setError('Failed to add item to cart');
    } finally {
      setLoading(false);
    }
  };

  // Update cart item quantity
  const updateCartItem = async (itemId, quantity) => {
    try {
      setLoading(true);

      if (isAuthenticated) {
        // Use API for authenticated users

        // First update the item
        const response = await cartService.updateCartItem(itemId, quantity);

        if (response.status === 'success') {
          // Then get a fresh cart from the server to ensure consistency
          const refreshResponse = await cartService.getCart();

          if (refreshResponse.status === 'success') {
            setCart(refreshResponse.data);
            triggerCartSync(refreshResponse.data);
          } else {
            // Fallback to the response from updateCartItem if refresh fails
            setCart(response.data);
            triggerCartSync(response.data);
          }
        }
      } else {
        // Use sessionStorage for unauthenticated users (temporary storage)
        const localCart = JSON.parse(sessionStorage.getItem('cart')) || [];
        let itemIndex = -1;

        // Check if itemId is a string that can be split (local format)
        if (typeof itemId === 'string' && itemId.includes('_')) {
          // Extract product ID and variant ID from local item ID
          const parts = itemId.split('_');
          if (parts.length >= 3) {
            const [_, productId, variantId] = parts;

            // Find the item in the local cart
            itemIndex = localCart.findIndex(item =>
              item.id.toString() === productId &&
              (variantId === '0' ? !item.variante_id : item.variante_id.toString() === variantId)
            );
          } else {
            // Invalid item ID format
          }
        } else {
          // Handle numeric or non-string itemId (direct item ID from API)

          // Find the item in the local cart that matches this ID
          itemIndex = localCart.findIndex(item => {
            // Convert both to strings for comparison
            return item.id.toString() === itemId.toString();
          });
        }



        if (itemIndex >= 0) {
          if (quantity <= 0) {
            // Remove item if quantity is 0 or less
            localCart.splice(itemIndex, 1);
          } else {
            // Update quantity
            localCart[itemIndex].quantite = quantity;
          }

          // Save to sessionStorage (temporary storage)
          sessionStorage.setItem('cart', JSON.stringify(localCart));

          // Create updated cart object
          const updatedCart = createLocalCartObject(localCart);

          // Update state
          setCart(updatedCart);

          // Update shared cart and trigger sync
          triggerCartSync(updatedCart);
        } else {
          setError('Item not found in cart');
        }
      }
    } catch (err) {
      setError('Failed to update cart item');
    } finally {
      setLoading(false);
    }
  };

  // Remove item from cart
  const removeFromCart = async (itemId) => {
    try {
      setLoading(true);

      if (isAuthenticated) {
        // Use API for authenticated users

        // First remove the item
        const response = await cartService.removeFromCart(itemId);

        if (response.status === 'success') {
          // Then get a fresh cart from the server to ensure consistency
          const refreshResponse = await cartService.getCart();

          if (refreshResponse.status === 'success') {
            setCart(refreshResponse.data);
            triggerCartSync(refreshResponse.data);
          } else {
            // Fallback to the response from removeFromCart if refresh fails
            setCart(response.data);
            triggerCartSync(response.data);
          }
        }
      } else {
        // Use sessionStorage for unauthenticated users (temporary storage)
        const localCart = JSON.parse(sessionStorage.getItem('cart')) || [];
        let updatedItems = [];

        // Check if itemId is a string that can be split (local format)
        if (typeof itemId === 'string' && itemId.includes('_')) {
          // Extract product ID and variant ID from local item ID
          const parts = itemId.split('_');
          if (parts.length >= 3) {
            const [_, productId, variantId] = parts;

            // Filter out the item
            updatedItems = localCart.filter(item =>
              !(item.id.toString() === productId &&
                (variantId === '0' ? !item.variante_id : item.variante_id.toString() === variantId))
            );
          } else {
            // Invalid item ID format
            updatedItems = localCart;
          }
        } else {
          // Handle numeric or non-string itemId (direct item ID from API)

          // Find the item in the local cart that matches this ID
          updatedItems = localCart.filter(item => {
            // Convert both to strings for comparison
            return item.id.toString() !== itemId.toString();
          });
        }

        // Save to sessionStorage (temporary storage)
        sessionStorage.setItem('cart', JSON.stringify(updatedItems));

        // Create updated cart object
        const updatedCart = createLocalCartObject(updatedItems);

        // Update state
        setCart(updatedCart);

        // Update shared cart and trigger sync
        triggerCartSync(updatedCart);
      }
    } catch (err) {
      setError('Failed to remove item from cart');
    } finally {
      setLoading(false);
    }
  };

  // Clear cart
  const clearCart = async () => {
    try {
      setLoading(true);

      if (isAuthenticated) {
        // Use API for authenticated users

        // First clear the cart
        const response = await cartService.clearCart();

        if (response.status === 'success') {
          // Then get a fresh cart from the server to ensure consistency
          const refreshResponse = await cartService.getCart();

          // Clear user-specific cart in localStorage
          if (user?.id) {
            localStorage.removeItem(`cart_user_${user.id}`);
          }
          if (keycloak?.tokenParsed?.sub) {
            localStorage.removeItem(`cart_user_${keycloak.tokenParsed.sub}`);
          }

          if (refreshResponse.status === 'success') {
            setCart(refreshResponse.data);
            triggerCartSync(refreshResponse.data);
          } else {
            // Fallback to empty cart if refresh fails
            const emptyCart = { items: [], nombre_items: 0, sous_total: 0, total: 0 };
            setCart(emptyCart);
            triggerCartSync(emptyCart);
          }
        }
      } else {
        // Use sessionStorage for unauthenticated users (temporary storage)
        sessionStorage.removeItem('cart');
        sessionStorage.removeItem('shared_cart');

        const emptyCart = { items: [], nombre_items: 0, sous_total: 0, total: 0 };
        setCart(emptyCart);
        triggerCartSync(emptyCart);
      }
    } catch (err) {
      setError('Failed to clear cart');
    } finally {
      setLoading(false);
    }
  };



  // Clear cart for current user
  const clearCartForCurrentUser = async () => {
    try {
      setLoading(true);

      if (isAuthenticated && keycloak?.tokenParsed?.sub) {

        // Clear the cart for the current user
        const response = await cartService.clearCartForUser(keycloak.tokenParsed.sub);

        if (response.status === 'success') {
          // Clear user-specific cart in localStorage
          if (user?.id) {
            localStorage.removeItem(`cart_user_${user.id}`);
          }
          if (keycloak?.tokenParsed?.sub) {
            localStorage.removeItem(`cart_user_${keycloak.tokenParsed.sub}`);
          }

          // Refresh the cart
          await refreshCart();
        }

        return true;
      }

      return false;
    } catch (err) {
      setError('Failed to clear cart for current user');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Value object to be provided to consumers
  const value = {
    cart,
    loading,
    error,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart,
    clearCartForCurrentUser
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

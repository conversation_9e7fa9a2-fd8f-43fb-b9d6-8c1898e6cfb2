import React, { useState, useEffect, useRef } from "react";
import { useParams, Link } from "react-router-dom";
import "../style/animations.css";
import LoadingSpinner from "../Components/LoadingSpinner";
import Chatbot from "@/Components/Chatbot";
import apiService from "../utils/apiService";
import EnhancedLazyImage from "../Components/EnhancedLazyImage";

// Custom CSS for animations
const customStyles = `
  @keyframes pulse-width {
    0% { width: 15%; }
    50% { width: 85%; }
    100% { width: 15%; }
  }

  .animate-pulse-width {
    animation: pulse-width 2s ease-in-out infinite;
  }
`;

const CategoryPage = () => {
  const { id } = useParams();
  const [category, setCategory] = useState(null);
  const [subCategories, setSubCategories] = useState([]);
  const [subCategoryImages, setSubCategoryImages] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [visibleItems, setVisibleItems] = useState([]);
  const headerRef = useRef(null);
  const itemsPerPage = 8; // Nombre d'éléments par page

  // Fonction pour suivre la position de défilement
  const handleScroll = () => {
    const position = window.pageYOffset;
    setScrollPosition(position);
  };

  // Effet pour gérer le défilement
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Effet pour observer les éléments visibles
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const handleIntersect = (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const id = entry.target.getAttribute('data-id');
          setVisibleItems(prev => [...prev, id]);
          observer.unobserve(entry.target);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersect, observerOptions);

    // Observer les éléments après le rendu
    const observeElements = () => {
      const elements = document.querySelectorAll('.subcategory-card');
      elements.forEach(el => observer.observe(el));
    };

    if (subCategories.length > 0) {
      setTimeout(observeElements, 100);
    }

    return () => {
      observer.disconnect();
    };
  }, [subCategories]);

  // Function to fetch subcategory images
  const fetchSubCategoryImages = async (subCategories) => {
    const imagePromises = subCategories.map(async (subCategory) => {
      try {
        const imageData = await apiService.get(
          '/images/get',
          { model_type: 'sous_categorie', model_id: subCategory.id },
          { useCache: true, cacheMaxAge: 600000, deduplicate: true }
        );

        // Handle both direct response and wrapped response
        const images = imageData.images || imageData.data?.images || [];

        if (images && images.length > 0) {
          // Find primary image or use the first one
          const primaryImage = images.find(img => img.is_primary) || images[0];

          return {
            id: subCategory.id,
            imageUrl: primaryImage.direct_url,
            thumbnailUrl: primaryImage.thumbnail_medium
          };
        }

        return { id: subCategory.id, imageUrl: null, thumbnailUrl: null };
      } catch (error) {
        if (process.env.NODE_ENV !== 'production') {
          console.warn(`Failed to fetch images for subcategory ${subCategory.id}:`, error);
        }
        return { id: subCategory.id, imageUrl: null, thumbnailUrl: null };
      }
    });

    const imageResults = await Promise.allSettled(imagePromises);
    const imagesMap = {};

    imageResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const { id, imageUrl, thumbnailUrl } = result.value;
        imagesMap[id] = { imageUrl, thumbnailUrl };
      } else {
        if (process.env.NODE_ENV !== 'production') {
          console.warn(`Failed to process images for subcategory ${subCategories[index].id}:`, result.reason);
        }
        // Fallback for failed requests
        imagesMap[subCategories[index].id] = { imageUrl: null, thumbnailUrl: null };
      }
    });

    setSubCategoryImages(imagesMap);
  };

  useEffect(() => {
    if (!id) {
      setError("ID de catégorie invalide.");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null); // Réinitialiser les erreurs à chaque nouvelle requête
    setVisibleItems([]); // Réinitialiser les éléments visibles
    setSubCategoryImages({}); // Reset images

    const fetchData = async () => {
      try {
        // Fetch category and subcategories in parallel
        const [categoryResponse, subCategoriesResponse] = await Promise.all([
          fetch(`https://laravel-api.fly.dev/api/categories/${id}`).then(res => res.json()),
          fetch(`https://laravel-api.fly.dev/api/sousCategories?categorie_id=${id}`).then(res => res.json())
        ]);

        setCategory(categoryResponse);

        const filteredSubCategories = subCategoriesResponse.filter(sub => sub.categorie_id == id);
        setSubCategories(filteredSubCategories);

        // Fetch images for subcategories
        if (filteredSubCategories.length > 0) {
          await fetchSubCategoryImages(filteredSubCategories);
        }

      } catch (error) {
        console.error('Error fetching data:', error);
        setError("Erreur lors du chargement des données.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Loading state with elegant animation
  if (loading) return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center" key={`category-loading-${id}`}>
      <div className="text-center max-w-md mx-auto px-4 py-12 bg-white rounded-lg shadow-md">
        <LoadingSpinner size="lg" variant="elegant" color="#A67B5B" />
        <p className="mt-6 text-gray-600 font-light tracking-wide">Chargement de la catégorie...</p>
        <div className="w-16 h-[0.5px] bg-[#A67B5B] mx-auto my-6 opacity-30"></div>
        <p className="text-sm text-gray-500 font-light">Veuillez patienter pendant que nous préparons votre expérience</p>
        <div className="mt-6 w-full bg-gray-100 h-2 rounded-full overflow-hidden">
          <div className="h-full bg-[#A67B5B] animate-pulse-width"></div>
        </div>
      </div>
    </div>
  );

  // Error state with improved styling
  if (error) return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center" key={`category-error-${id}`}>
      <div className="text-center max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-red-50 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <h2 className="text-2xl font-light text-gray-800 mb-4">Une erreur est survenue</h2>
        <div className="w-16 h-[0.5px] bg-[#A67B5B] mx-auto my-4 opacity-30"></div>
        <p className="text-gray-600 mb-8 max-w-md">{error}</p>
        <Link to="/" className="flex items-center bg-[#A67B5B] text-white px-8 py-3 rounded-lg font-medium shadow-md hover:bg-[#8B5A2B] hover:shadow-lg transition-all duration-300 mx-auto w-max">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <span>Retour à l'accueil</span>
        </Link>
      </div>
    </div>
  );

  // Calcul des éléments affichés avec pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = subCategories.slice(indexOfFirstItem, indexOfLastItem);

  // Fonction pour vérifier si un élément est visible
  const isItemVisible = (id) => visibleItems.includes(id.toString());

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f9f6f3] to-[#fff7f0] text-gray-800 font-serif" key={`category-page-${id}`}>
      <Chatbot />
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />
      {/* En-tête avec image de la catégorie et effet parallaxe */}
      {category && category.image_categorie && (
        <div
          ref={headerRef}
          className="relative w-full h-[420px] md:h-[520px] overflow-hidden rounded-b-3xl shadow-xl mb-8"
          style={{ perspective: '1000px', transformStyle: 'preserve-3d' }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-10" />
          <div
            className="absolute inset-0 w-full h-full"
            style={{
              transform: `translateY(${scrollPosition * 0.3}px)`,
              transition: 'transform 0.1s ease-out'
            }}
          >
            <img
              src={category.image_categorie}
              alt={category.nom_categorie}
              className="w-full h-full object-cover animate-zoom-in scale-105"
              style={{ transformOrigin: 'center center', objectPosition: 'center center' }}
            />
          </div>
          <div className="absolute inset-0 flex flex-col items-center justify-center z-20">
            <div className="text-center px-8 py-10 bg-white/20 backdrop-blur-md rounded-xl border border-white/30 shadow-2xl">
              <h1 className="text-5xl md:text-6xl font-extralight tracking-widest text-white mb-4 animate-fade-in drop-shadow-lg">
                {category?.nom_categorie}
              </h1>
              <div className="w-32 h-px bg-[#A67B5B] mx-auto my-4 animate-slide-in"></div>
              <p className="text-xl text-white/90 animate-fade-in font-light">
                <span className="font-semibold text-[#FFD7B0]">{subCategories.length}</span> sous-catégories disponibles
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Description de la catégorie */}


      {/* Liste des sous-catégories avec pagination et animations */}
      <div className="container mx-auto px-4 pb-16 pt-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10">
          {currentItems.map((subCategory, index) => (
            <Link
              to={`/sous_souscategorie/${subCategory.id}`}
              key={subCategory.id}
              data-id={subCategory.id}
              className={`subcategory-card bg-white/90 rounded-2xl overflow-hidden border border-[#A67B5B]/10 shadow-lg hover:shadow-2xl parallax-card transform transition-all duration-700 ${
                isItemVisible(subCategory.id) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-16'
              }`}
              style={{
                transitionDelay: `${(index % 4) * 120}ms`,
                boxShadow: '0 10px 25px -5px rgba(166,123,91,0.08), 0 8px 10px -6px rgba(166,123,91,0.03)'
              }}
            >
              <div className="relative overflow-hidden h-56 group">
                <EnhancedLazyImage
                  src={subCategoryImages[subCategory.id]?.imageUrl || subCategoryImages[subCategory.id]?.thumbnailUrl}
                  alt={subCategory.nom_sous_categorie}
                  className=""
                  fallbackSrc="https://via.placeholder.com/400x300?text=Sous-catégorie"
                  spinnerVariant="circle"
                  placeholderColor="#f3f4f6"
                  optimize={false}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#A67B5B]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-10"></div>
                <span className="absolute top-3 left-3 bg-[#FFD7B0]/90 text-[#A67B5B] px-3 py-1 rounded-full text-xs font-semibold shadow-md z-20">
                  {subCategory.nom_sous_categorie}
                </span>
              </div>
              <div className="p-6 text-center">
                <h3 className="text-xl font-light text-[#A67B5B] mb-2 depth-element depth-element-2">
                  <span className="relative inline-block underline-animation">
                    {subCategory.nom_sous_categorie}
                  </span>
                </h3>
                <p className="text-gray-600 text-sm mt-2 depth-element depth-element-1 min-h-[48px]">
                  {subCategory.description_sous_categorie}
                </p>
                <div className="mt-4 depth-element depth-element-3">
                  <span className="inline-block px-4 py-2 bg-[#A67B5B]/10 text-[#A67B5B] border border-[#A67B5B] rounded-md hover:bg-[#A67B5B] hover:text-white transition-all duration-300 cursor-pointer">
                    Découvrir
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Pagination élégante */}
        {subCategories.length > itemsPerPage && (
          <div className="flex justify-center mt-12">
            <div className="inline-flex rounded-xl overflow-hidden shadow-lg bg-white/90 p-1 border border-[#A67B5B]/10">
              {Array.from({ length: Math.ceil(subCategories.length / itemsPerPage) }, (_, i) => (
                <button
                  key={i}
                  className={`px-5 py-3 mx-1 rounded-lg transition-all duration-300 font-semibold tracking-wide ${
                    currentPage === i + 1
                      ? "bg-[#A67B5B] text-white shadow-md"
                      : "bg-transparent text-[#A67B5B] hover:bg-[#FFD7B0]/40"
                  }`}
                  onClick={() => setCurrentPage(i + 1)}
                >
                  {i + 1}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryPage;

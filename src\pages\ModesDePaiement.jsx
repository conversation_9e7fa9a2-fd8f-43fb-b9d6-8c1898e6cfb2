import Chatbot from "@/Components/Chatbot";
import React from "react";

export default function ModesDePaiement() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex flex-col items-center justify-center py-16 px-4">
      <div className="max-w-2xl w-full bg-white rounded-2xl shadow-xl p-10 relative overflow-hidden">
        <h1 className="text-3xl md:text-4xl font-light text-center mb-6 tracking-widest text-[#A67B5B]">Modes de paiement</h1>
        <div className="w-20 h-1 bg-[#A67B5B] mx-auto mb-8"></div>
        <Chatbot />
        <p className="text-lg text-gray-700 mb-6 text-center">
          Nous vous proposons plusieurs solutions de paiement pour faciliter vos achats en boutique ou en ligne :
        </p>
        <ul className="mb-8 space-y-4">
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-credit-card text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Carte bancaire (Visa, Mastercard)</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-money-bill-wave text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Espèces en boutique</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-building-columns text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Virement bancaire</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-brands fa-cc-visa text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Paiement à la livraison (dans certaines zones)</span>
          </li>
        </ul>
        <p className="text-center text-gray-500 text-sm">
          Pour toute question sur les modalités de paiement, n'hésitez pas à contacter notre service client.
        </p>
      </div>
    </div>
  );
}

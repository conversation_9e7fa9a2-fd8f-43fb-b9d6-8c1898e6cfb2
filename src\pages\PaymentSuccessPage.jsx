import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import LoadingSpinner from '../Components/LoadingSpinner.jsx';
import paymentService from '../Services/payment.service.js';
import { PAYMENT_STATUS } from '../Services/stripe.config.js';

const PaymentSuccessPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [paymentStatus, setPaymentStatus] = useState('loading');
  const [paymentDetails, setPaymentDetails] = useState(null);
  const [error, setError] = useState(null);

  const paymentIntent = searchParams.get('payment_intent');
  const paymentIntentClientSecret = searchParams.get('payment_intent_client_secret');

  useEffect(() => {
    const verifyPayment = async () => {
      if (!paymentIntent) {
        setError('Aucun identifiant de paiement trouvé dans l\'URL');
        setPaymentStatus('error');
        return;
      }

      try {
        const response = await paymentService.getPaymentIntentStatus(paymentIntent);
        
        if (response.status === 'success') {
          setPaymentDetails(response);
          
          // Check the payment status
          const status = response.payment_intent?.status;
          if (status === PAYMENT_STATUS.SUCCEEDED) {
            setPaymentStatus('success');
          } else {
            setPaymentStatus(status || 'unknown');
          }
        } else {
          setError(response.message || 'Erreur lors de la vérification du paiement');
          setPaymentStatus('error');
        }
      } catch (err) {
        setError('Erreur de connexion lors de la vérification du paiement');
        setPaymentStatus('error');
      }
    };

    verifyPayment();
  }, [paymentIntent]);

  const formatAmount = (amount, currency) => {
    if (!amount) return '0.00';
    const value = (amount / 100).toFixed(2); // Stripe amounts are in cents
    const symbol = currency === 'eur' ? '€' : currency.toUpperCase();
    return `${value} ${symbol}`;
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '-';
    const date = new Date(timestamp * 1000); // Stripe timestamps are in seconds
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (paymentStatus === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" variant="elegant" color="#A67B5B" />
          <p className="mt-4 text-gray-600">Vérification du paiement...</p>
        </div>
      </div>
    );
  }

  if (paymentStatus === 'error') {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
              <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-2xl font-medium text-gray-900 mb-4">
              Erreur de vérification du paiement
            </h2>
            <p className="text-gray-600 mb-6">
              {error || 'Une erreur est survenue lors de la vérification de votre paiement.'}
            </p>
            <div className="space-y-3">
              <Link
                to="/commandes"
                className="inline-block w-full bg-[#A67B5B] text-white px-4 py-2 rounded-md hover:bg-[#8B5A2B] transition-colors"
              >
                Voir mes commandes
              </Link>
              <Link
                to="/products"
                className="inline-block w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
              >
                Continuer mes achats
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (paymentStatus === 'success') {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {/* Success Header */}
            <div className="bg-green-50 px-6 py-8 text-center border-b border-green-200">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-3xl font-medium text-gray-900 mb-2">
                Paiement réussi !
              </h1>
              <p className="text-gray-600">
                Votre paiement a été traité avec succès. Un email de confirmation vous a été envoyé.
              </p>
            </div>

            {/* Payment Details */}
            <div className="px-6 py-8">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Détails du paiement
              </h2>
              
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">ID de transaction</span>
                  <span className="font-mono text-sm">{paymentIntent}</span>
                </div>
                
                {paymentDetails?.payment_intent && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Montant</span>
                      <span className="font-medium">
                        {formatAmount(paymentDetails.payment_intent.amount, paymentDetails.payment_intent.currency)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Date et heure</span>
                      <span className="font-medium">
                        {formatDate(paymentDetails.payment_intent.created)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Statut</span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Payé
                      </span>
                    </div>
                  </>
                )}

                {paymentDetails?.local_payment && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Commande #</span>
                      <span className="font-medium">
                        {paymentDetails.local_payment.commande_id}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Statut de la commande</span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {paymentDetails.local_payment.commande_status === 'paid' ? 'Payée' : paymentDetails.local_payment.commande_status}
                      </span>
                    </div>
                  </>
                )}
              </div>

              {/* Next Steps */}
              <div className="mt-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Prochaines étapes
                </h3>
                <div className="bg-blue-50 rounded-lg p-4">
                  <ul className="space-y-2 text-sm text-blue-800">
                    <li className="flex items-start">
                      <span className="flex-shrink-0 h-5 w-5 text-blue-600 mr-2">✓</span>
                      Votre commande est maintenant confirmée et sera traitée
                    </li>
                    <li className="flex items-start">
                      <span className="flex-shrink-0 h-5 w-5 text-blue-600 mr-2">✓</span>
                      Un email de confirmation avec les détails de votre commande vous a été envoyé
                    </li>
                    <li className="flex items-start">
                      <span className="flex-shrink-0 h-5 w-5 text-blue-600 mr-2">✓</span>
                      Vous recevrez une notification dès que votre commande sera expédiée
                    </li>
                  </ul>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                {paymentDetails?.local_payment?.commande_id ? (
                  <Link
                    to={`/commandes/${paymentDetails.local_payment.commande_id}`}
                    className="flex-1 bg-[#A67B5B] text-white text-center px-4 py-2 rounded-md hover:bg-[#8B5A2B] transition-colors"
                  >
                    Voir les détails de la commande
                  </Link>
                ) : (
                  <Link
                    to="/commandes"
                    className="flex-1 bg-[#A67B5B] text-white text-center px-4 py-2 rounded-md hover:bg-[#8B5A2B] transition-colors"
                  >
                    Voir mes commandes
                  </Link>
                )}
                
                <Link
                  to="/products"
                  className="flex-1 bg-gray-200 text-gray-800 text-center px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Continuer mes achats
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle other payment statuses
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
            <svg className="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-2xl font-medium text-gray-900 mb-4">
            Paiement en cours
          </h2>
          <p className="text-gray-600 mb-6">
            Votre paiement est en cours de traitement. Statut actuel: {paymentStatus}
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="inline-block w-full bg-[#A67B5B] text-white px-4 py-2 rounded-md hover:bg-[#8B5A2B] transition-colors"
            >
              Actualiser le statut
            </button>
            <Link
              to="/commandes"
              className="inline-block w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
            >
              Voir mes commandes
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
